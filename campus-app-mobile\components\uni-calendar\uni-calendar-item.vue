<template>
	<view class="uni-calendar-item__weeks-box" :class="{
		'uni-calendar-item--disable':weeks.disable,
		'uni-calendar-item--isDay':calendar.fullDate === weeks.fullDate && weeks.isDay,
		'uni-calendar-item--checked':(calendar.fullDate === weeks.fullDate && !weeks.isDay) ,
		'uni-calendar-item--before-checked':weeks.beforeMultiple,
		'uni-calendar-item--multiple': weeks.multiple,
		'uni-calendar-item--after-checked':weeks.afterMultiple,
		}" @click="choiceDate(weeks)">
		<view class="uni-calendar-item__weeks-box-item">
			<text v-if="selected&&weeks.extraInfo" class="uni-calendar-item__weeks-box-circle"></text>
			<text class="uni-calendar-item__weeks-box-text" :class="{
				'uni-calendar-item--isDay-text': weeks.isDay,
				'uni-calendar-item--isDay':calendar.fullDate === weeks.fullDate && weeks.isDay,
				'uni-calendar-item--checked':calendar.fullDate === weeks.fullDate && !weeks.isDay,
				'uni-calendar-item--before-checked':weeks.beforeMultiple,
				'uni-calendar-item--multiple': weeks.multiple,
				'uni-calendar-item--after-checked':weeks.afterMultiple,
				'uni-calendar-item--disable':weeks.disable,
				}">{{weeks.date}}</text>
			<text v-if="!lunar&&!weeks.extraInfo && weeks.isDay" class="uni-calendar-item__weeks-lunar-text" :class="{
				'uni-calendar-item--isDay-text':weeks.isDay,
				'uni-calendar-item--isDay':calendar.fullDate === weeks.fullDate && weeks.isDay,
				'uni-calendar-item--checked':calendar.fullDate === weeks.fullDate && !weeks.isDay,
				'uni-calendar-item--before-checked':weeks.beforeMultiple,
				'uni-calendar-item--multiple': weeks.multiple,
				'uni-calendar-item--after-checked':weeks.afterMultiple,
				}">{{todayText}}</text>
			<text v-if="lunar&&!weeks.extraInfo" class="uni-calendar-item__weeks-lunar-text" :class="{
				'uni-calendar-item--isDay-text':weeks.isDay,
				'uni-calendar-item--isDay':calendar.fullDate === weeks.fullDate && weeks.isDay,
				'uni-calendar-item--checked':calendar.fullDate === weeks.fullDate && !weeks.isDay,
				'uni-calendar-item--before-checked':weeks.beforeMultiple,
				'uni-calendar-item--multiple': weeks.multiple,
				'uni-calendar-item--after-checked':weeks.afterMultiple,
				'uni-calendar-item--disable':weeks.disable,
				}">{{weeks.isDay ? todayText : (weeks.lunar.IDayCn === '初一'?weeks.lunar.IMonthCn:weeks.lunar.IDayCn)}}</text>
			<view v-if="weeks.extraInfo&&weeks.extraInfo.info&&weeks.extraInfo.info.length >0" class="tag-container">
				<!-- 第一个标签（课程）单独处理，居中显示 -->
				<view v-if="weeks.extraInfo.info[0]" class="tag-wrapper first-tag-wrapper">
					<view class="tag-item no-border">
						<text :style="{color: weeks.extraInfo.info[0].color}" class="uni-calendar-item__weeks-lunar-text tag-text" :class="{
										'uni-calendar-item--extra':weeks.extraInfo.info,
										'uni-calendar-item--isDay-text':weeks.isDay,
										'uni-calendar-item--isDay':calendar.fullDate === weeks.fullDate && weeks.isDay,
										'uni-calendar-item--checked':calendar.fullDate === weeks.fullDate && !weeks.isDay,
										'uni-calendar-item--before-checked':weeks.beforeMultiple,
										'uni-calendar-item--multiple': weeks.multiple,
										'uni-calendar-item--after-checked':weeks.afterMultiple,
										'uni-calendar-item--disable':weeks.disable,
										}">{{weeks.extraInfo.info[0].text}}</text>
					</view>
				</view>
				
				<!-- 上午和下午标签 -->
				<view v-for="(item, index) in weeks.extraInfo.info.slice(1)" :key="index+1" class="tag-wrapper">
					<text v-if="index === 0" class="tag-prefix">上</text>
					<text v-if="index === 1" class="tag-prefix">下</text>
					
					<view class="tag-item" :class="{'morning': index === 0, 'afternoon': index === 1}" :style="{ borderColor: item.color }">
						<text :style="{color: item.color}" class="uni-calendar-item__weeks-lunar-text tag-text" :class="{
											'uni-calendar-item--extra':weeks.extraInfo.info,
											'uni-calendar-item--isDay-text':weeks.isDay,
											'uni-calendar-item--isDay':calendar.fullDate === weeks.fullDate && weeks.isDay,
											'uni-calendar-item--checked':calendar.fullDate === weeks.fullDate && !weeks.isDay,
											'uni-calendar-item--before-checked':weeks.beforeMultiple,
											'uni-calendar-item--multiple': weeks.multiple,
											'uni-calendar-item--after-checked':weeks.afterMultiple,
											'uni-calendar-item--disable':weeks.disable,
											}">{{item.text}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		initVueI18n
	} from '@dcloudio/uni-i18n'
	import i18nMessages from './i18n/index.js'
	const {
		t
	} = initVueI18n(i18nMessages)

	export default {
		emits: ['change'],
		props: {
			weeks: {
				type: Object,
				default () {
					return {}
				}
			},
			calendar: {
				type: Object,
				default: () => {
					return {}
				}
			},
			selected: {
				type: Array,
				default: () => {
					return []
				}
			},
			lunar: {
				type: Boolean,
				default: false
			}
		},
		computed: {
			todayText() {
				return t("uni-calender.today")
			},
		},
		methods: {
			choiceDate(weeks) {
				this.$emit('change', weeks)
			}
		}
	}
</script>

<style lang="scss" scoped>
	$uni-font-size-base: 14px;
	$uni-text-color: #333;
	$uni-font-size-sm: 12px;
	$uni-color-error: #e43d33;
	$uni-opacity-disabled: 0.3;
	$uni-text-color-disable: #c0c0c0;
	$uni-primary: #ffd085 !default;

	.uni-calendar-item__weeks-box {
		flex: 1;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		align-items: center;
		height: 80px;
		
	}

	.uni-calendar-item__weeks-box-text {
		font-size: $uni-font-size-base;
		color: $uni-text-color;
	}

	.uni-calendar-item__weeks-lunar-text {
		font-size: $uni-font-size-sm;
		color: $uni-text-color;
	}

	.uni-calendar-item__weeks-box-item {
		padding-top: 4px;
		position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: 100%;
	}

	.uni-calendar-item__weeks-box-circle {
		position: absolute;
		top: 5px;
		right: 5px;
		width: 8px;
		height: 8px;
		border-radius: 8px;
		// background-color: $uni-color-error;
	}

	.tag-container {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
	
	}

	.tag-wrapper {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		margin-top: 2px;
	
		
	}

	.tag-item {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		padding: 1px 2px;
		border-radius: 5px;
		border: 1px solid;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	.no-border {
		border: none !important;
		background-color: transparent !important;
		padding-left: 0;
		justify-content: center;
		font-weight: bold;
		font-size: 10px;
		position: relative;
	}

	.no-border .tag-text {
		text-align: center;
		font-weight: bold;
		font-size: 11px !important;
		position: relative;
	}

	.no-border .tag-text::after {
		content: '';
		position: absolute;
		bottom: -3px;
		left: 50%;
		transform: translateX(-50%);
		width: 85%;
		height: 1px;
		background-color: #eee;
	}

	.tag-text {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		font-size: 9px !important;
		text-align: left;
	}

	.tag-prefix-empty {
		width: 0;
		font-size: 9px;

	}

	.tag-prefix {
		font-size: 9px;
		color: #2e2d2d;
		width: 12px; /* 固定前缀宽度 */
		text-align: center;
		margin-right: 3px;
	}

	.morning {
		
	}

	.afternoon {
		
	}

	.uni-calendar-item--disable {
		background-color: rgba(249, 249, 249, $uni-opacity-disabled);
		color: $uni-text-color-disable;
	}

	.uni-calendar-item--isDay-text {
		color: $uni-primary;
	}

	.uni-calendar-item--isDay {
		background-color: $uni-primary;
		opacity: 0.8;
		color: #fff;
	}

	.uni-calendar-item--extra {
		color: $uni-color-error;
		opacity: 0.8;
	}

	.uni-calendar-item--checked {
		background-color: $uni-primary;
		color: #fff;
		opacity: 0.8;
	}

	.uni-calendar-item--multiple {
		background-color: $uni-primary;
		color: #fff;
		opacity: 0.8;
	}

	.uni-calendar-item--before-checked {
		background-color: #ff5a5f;
		color: #fff;
	}

	.uni-calendar-item--after-checked {
		background-color: #ff5a5f;
		color: #fff;
	}

	.first-tag-wrapper {
		width: 85% !important;
		justify-content: center;
		
	}
</style>