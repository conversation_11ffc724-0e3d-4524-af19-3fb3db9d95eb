<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Excel文件读取器</title>
    <!-- 引入 ExcelJS 库 -->
    <script src="http://www.resource.szwgft.cn/oss/exceljs.min.js"></script>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }

      .container {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      h1 {
        color: #333;
        text-align: center;
        margin-bottom: 30px;
      }

      .upload-area {
        border: 2px dashed #ddd;
        border-radius: 10px;
        padding: 40px;
        text-align: center;
        margin-bottom: 30px;
        transition: border-color 0.3s;
      }

      .upload-area:hover {
        border-color: #007bff;
      }

      .upload-area.dragover {
        border-color: #007bff;
        background-color: #f8f9fa;
      }

      input[type='file'] {
        display: none;
      }

      .upload-btn {
        background-color: #007bff;
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
        transition: background-color 0.3s;
      }

      .upload-btn:hover {
        background-color: #0056b3;
      }

      .file-info {
        margin: 20px 0;
        padding: 15px;
        background-color: #e9ecef;
        border-radius: 5px;
        display: none;
      }

      .sheet-selector {
        margin: 20px 0;
        display: none;
      }

      .sheet-selector select {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 14px;
      }

      .data-container {
        margin-top: 30px;
        display: none;
      }

      .table-wrapper {
        overflow-x: auto;
        max-height: 600px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        background: white;
      }

      th,
      td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #ddd;
        white-space: nowrap;
      }

      th {
        background-color: #f8f9fa;
        font-weight: bold;
        position: sticky;
        top: 0;
        z-index: 10;
      }

      tr:hover {
        background-color: #f5f5f5;
      }

      .stats {
        margin: 20px 0;
        padding: 15px;
        background-color: #d4edda;
        border-radius: 5px;
        border-left: 4px solid #28a745;
      }

      .error {
        margin: 20px 0;
        padding: 15px;
        background-color: #f8d7da;
        border-radius: 5px;
        border-left: 4px solid #dc3545;
        color: #721c24;
      }

      .loading {
        text-align: center;
        padding: 20px;
        color: #666;
      }

      .control-panel {
        margin: 20px 0;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #dee2e6;
      }

      .control-panel h3 {
        margin: 0 0 15px 0;
        color: #495057;
        font-size: 18px;
      }

      .control-row {
        margin: 10px 0;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .control-row label {
        min-width: 120px;
        font-weight: 500;
        color: #495057;
      }

      .control-row select {
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
        min-width: 200px;
      }

      .control-buttons {
        margin: 20px 0 10px 0;
        display: flex;
        gap: 15px;
      }

      .process-btn,
      .export-btn {
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s;
      }

      .process-btn {
        background-color: #28a745;
        color: white;
      }

      .process-btn:hover {
        background-color: #218838;
      }

      .export-btn {
        background-color: #17a2b8;
        color: white;
      }

      .export-btn:hover:not(:disabled) {
        background-color: #138496;
      }

      .export-btn:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
        opacity: 0.6;
      }

      .process-info {
        margin: 15px 0;
        padding: 15px;
        background-color: #d1ecf1;
        border-radius: 5px;
        border-left: 4px solid #17a2b8;
        color: #0c5460;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>📊 Excel文件读取器</h1>

      <div class="upload-area" id="uploadArea">
        <p>📁 拖拽Excel文件到这里，或者点击下方按钮选择文件</p>
        <button class="upload-btn" onclick="document.getElementById('fileInput').click()">选择Excel文件</button>
        <input type="file" id="fileInput" accept=".xlsx,.xls" />
        <p style="margin-top: 15px; color: #666; font-size: 14px">支持 .xlsx 和 .xls 格式</p>
      </div>

      <div class="file-info" id="fileInfo"></div>

      <div class="sheet-selector" id="sheetSelector">
        <label for="sheetSelect">选择工作表：</label>
        <select id="sheetSelect"></select>
      </div>

      <div class="loading" id="loading" style="display: none">正在读取文件，请稍候...</div>

      <div class="data-container" id="dataContainer">
        <div class="stats" id="stats"></div>

        <!-- 数据处理控制面板 -->
        <div class="control-panel" id="controlPanel" style="display: none">
          <h3>数据处理选项</h3>
          <div class="control-row">
            <label for="timeColumn">采集时间字段：</label>
            <select id="timeColumn"></select>
          </div>
          <div class="control-row">
            <label for="pressureColumn">末端压力字段：</label>
            <select id="pressureColumn"></select>
          </div>
          <div class="control-buttons">
            <button class="process-btn" onclick="processData()">处理数据 (6秒→1分钟)</button>
            <button class="export-btn" onclick="exportProcessedData()" disabled id="exportBtn">导出处理后数据</button>
          </div>
          <div class="process-info" id="processInfo" style="display: none"></div>
        </div>

        <div class="table-wrapper">
          <table id="dataTable">
            <thead id="tableHead"></thead>
            <tbody id="tableBody"></tbody>
          </table>
        </div>
      </div>
    </div>

    <script>
      let workbook = null
      let currentData = []
      let processedData = []

      // 文件输入处理
      const fileInput = document.getElementById('fileInput')
      const uploadArea = document.getElementById('uploadArea')
      const fileInfo = document.getElementById('fileInfo')
      const sheetSelector = document.getElementById('sheetSelector')
      const sheetSelect = document.getElementById('sheetSelect')
      const dataContainer = document.getElementById('dataContainer')
      const loading = document.getElementById('loading')
      const stats = document.getElementById('stats')
      const tableHead = document.getElementById('tableHead')
      const tableBody = document.getElementById('tableBody')

      // 文件选择事件
      fileInput.addEventListener('change', handleFileSelect)

      // 拖拽事件
      uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault()
        uploadArea.classList.add('dragover')
      })

      uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover')
      })

      uploadArea.addEventListener('drop', (e) => {
        e.preventDefault()
        uploadArea.classList.remove('dragover')
        const files = e.dataTransfer.files
        if (files.length > 0) {
          handleFile(files[0])
        }
      })

      // 工作表选择事件
      sheetSelect.addEventListener('change', () => {
        const selectedSheet = sheetSelect.value
        if (selectedSheet && workbook) {
          displaySheetData(selectedSheet)
        }
      })

      function handleFileSelect(event) {
        const file = event.target.files[0]
        if (file) {
          handleFile(file)
        }
      }

      async function handleFile(file) {
        // 验证文件类型
        if (!file.name.match(/\.(xlsx|xls)$/i)) {
          showError('请选择有效的Excel文件（.xlsx 或 .xls）')
          return
        }

        // 显示文件信息
        showFileInfo(file)

        // 显示加载状态
        loading.style.display = 'block'
        dataContainer.style.display = 'none'
        sheetSelector.style.display = 'none'

        try {
          // 读取文件
          const arrayBuffer = await file.arrayBuffer()
          workbook = new ExcelJS.Workbook()
          await workbook.xlsx.load(arrayBuffer)

          // 显示工作表选择器
          populateSheetSelector()
          sheetSelector.style.display = 'block'

          // 默认显示第一个工作表
          if (workbook.worksheets.length > 0) {
            sheetSelect.value = workbook.worksheets[0].name
            displaySheetData(workbook.worksheets[0].name)
          }
        } catch (error) {
          console.error('读取文件时出错:', error)
          showError('读取文件时出错: ' + error.message)
        } finally {
          loading.style.display = 'none'
        }
      }

      function showFileInfo(file) {
        const sizeInMB = (file.size / (1024 * 1024)).toFixed(2)
        fileInfo.innerHTML = `
          <strong>文件信息：</strong><br>
          文件名: ${file.name}<br>
          文件大小: ${sizeInMB} MB<br>
          最后修改: ${new Date(file.lastModified).toLocaleString()}
        `
        fileInfo.style.display = 'block'
      }

      function populateSheetSelector() {
        sheetSelect.innerHTML = ''
        workbook.worksheets.forEach((worksheet) => {
          const option = document.createElement('option')
          option.value = worksheet.name
          option.textContent = `${worksheet.name} (${worksheet.rowCount} 行)`
          sheetSelect.appendChild(option)
        })
      }

      function displaySheetData(sheetName) {
        const worksheet = workbook.getWorksheet(sheetName)
        if (!worksheet) {
          showError('找不到指定的工作表')
          return
        }

        // 获取数据
        const data = []
        const headers = []

        // 获取表头（第一行）
        const firstRow = worksheet.getRow(1)
        firstRow.eachCell((cell, colNumber) => {
          headers.push(cell.value || `列${colNumber}`)
        })

        // 获取数据行
        for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
          const row = worksheet.getRow(rowNumber)
          const rowData = {}
          let hasData = false

          row.eachCell((cell, colNumber) => {
            const header = headers[colNumber - 1] || `列${colNumber}`
            let cellValue = cell.value

            // 处理特殊值类型
            if (cellValue && typeof cellValue === 'object') {
              if (cellValue.formula) {
                cellValue = cellValue.result || cellValue.formula
              } else if (cellValue.text) {
                cellValue = cellValue.text
              } else {
                cellValue = String(cellValue)
              }
            }

            rowData[header] = cellValue
            if (cellValue !== null && cellValue !== undefined && cellValue !== '') {
              hasData = true
            }
          })

          if (hasData) {
            data.push(rowData)
            console.log(rowData)
          }
        }

        currentData = data
        console.log(currentData)

        // 显示统计信息
        showStats(headers.length, data.length)

        // 显示表格
        displayTable(headers, data)

        // 显示控制面板并填充字段选项
        populateColumnSelectors(headers)
        document.getElementById('controlPanel').style.display = 'block'

        dataContainer.style.display = 'block'
      }

      function showStats(columnCount, rowCount) {
        stats.innerHTML = `
          <strong>数据统计：</strong>
          共 ${columnCount} 列，${rowCount} 行数据
        `
      }

      function displayTable(headers, data) {
        // 创建表头
        tableHead.innerHTML = ''
        const headerRow = document.createElement('tr')
        headers.forEach((header) => {
          const th = document.createElement('th')
          th.textContent = header
          headerRow.appendChild(th)
        })
        tableHead.appendChild(headerRow)

        // 创建表格内容
        tableBody.innerHTML = ''
        data.forEach((row, index) => {
          const tr = document.createElement('tr')
          headers.forEach((header) => {
            const td = document.createElement('td')
            const value = row[header]
            td.textContent = value !== null && value !== undefined ? String(value) : ''
            tr.appendChild(td)
          })
          tableBody.appendChild(tr)
        })
      }

      function showError(message) {
        const errorDiv = document.createElement('div')
        errorDiv.className = 'error'
        errorDiv.innerHTML = `<strong>错误：</strong> ${message}`

        // 移除之前的错误信息
        const existingError = document.querySelector('.error')
        if (existingError) {
          existingError.remove()
        }

        // 在容器开头插入错误信息
        const container = document.querySelector('.container')
        container.insertBefore(errorDiv, container.children[1])

        // 3秒后自动移除错误信息
        setTimeout(() => {
          errorDiv.remove()
        }, 5000)
      }

      // 添加一些实用功能
      function exportCurrentData() {
        if (currentData.length === 0) {
          showError('没有数据可导出')
          return
        }

        const csv = convertToCSV(currentData)
        downloadCSV(csv, 'exported_data.csv')
      }

      function convertToCSV(data) {
        if (data.length === 0) return ''

        const headers = Object.keys(data[0])
        const csvHeaders = headers.join(',')
        const csvRows = data.map((row) =>
          headers
            .map((header) => {
              const value = row[header]
              // 处理包含逗号或引号的值
              if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                return `"${value.replace(/"/g, '""')}"`
              }
              return value || ''
            })
            .join(',')
        )

        return [csvHeaders, ...csvRows].join('\n')
      }

      function downloadCSV(csv, filename) {
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', filename)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }

      // 填充字段选择器
      function populateColumnSelectors(headers) {
        const timeColumn = document.getElementById('timeColumn')
        const pressureColumn = document.getElementById('pressureColumn')

        // 清空选项
        timeColumn.innerHTML = '<option value="">请选择...</option>'
        pressureColumn.innerHTML = '<option value="">请选择...</option>'

        headers.forEach((header) => {
          const timeOption = document.createElement('option')
          timeOption.value = header
          timeOption.textContent = header
          timeColumn.appendChild(timeOption)

          const pressureOption = document.createElement('option')
          pressureOption.value = header
          pressureOption.textContent = header
          pressureColumn.appendChild(pressureOption)
        })

        // 尝试自动匹配字段
        headers.forEach((header) => {
          const lowerHeader = header.toLowerCase()
          if (lowerHeader.includes('时间') || lowerHeader.includes('time') || lowerHeader.includes('date')) {
            timeColumn.value = header
          }
          if (lowerHeader.includes('压力') || lowerHeader.includes('pressure')) {
            pressureColumn.value = header
          }
        })
      }

      // 数据处理主函数
      function processData() {
        const timeColumnName = document.getElementById('timeColumn').value
        const pressureColumnName = document.getElementById('pressureColumn').value

        if (!timeColumnName || !pressureColumnName) {
          showError('请选择采集时间和末端压力字段')
          return
        }

        if (currentData.length === 0) {
          showError('没有数据可处理')
          return
        }

        try {
          // 显示处理信息
          const processInfo = document.getElementById('processInfo')
          processInfo.innerHTML = '正在处理数据，请稍候...'
          processInfo.style.display = 'block'

          // 处理数据
          processedData = processTimeSeriesData(currentData, timeColumnName, pressureColumnName)

          // 更新界面
          displayProcessedData()

          // 启用导出按钮
          document.getElementById('exportBtn').disabled = false

          processInfo.innerHTML = `
            <strong>处理完成！</strong><br>
            原始数据：${currentData.length} 条<br>
            处理后数据：${processedData.length} 条<br>
            数据颗粒度：从 6秒 转换为 1分钟
          `
        } catch (error) {
          console.error('数据处理错误:', error)
          showError('数据处理失败: ' + error.message)
        }
      }

      // 时间序列数据处理核心算法
      function processTimeSeriesData(data, timeColumn, pressureColumn) {
        // 1. 数据预处理和排序
        const validData = data
          .filter((row) => {
            const timeValue = row[timeColumn]
            const pressureValue = row[pressureColumn]
            return timeValue && pressureValue !== null && pressureValue !== undefined
          })
          .map((row) => {
            const timeValue = row[timeColumn]
            let parsedTime

            // 尝试解析时间
            if (timeValue instanceof Date) {
              parsedTime = timeValue
            } else if (typeof timeValue === 'string') {
              parsedTime = new Date(timeValue)
            } else if (typeof timeValue === 'number') {
              // Excel 时间戳转换
              parsedTime = new Date((timeValue - 25569) * 86400 * 1000)
            } else {
              throw new Error(`无法解析时间格式: ${timeValue}`)
            }

            if (isNaN(parsedTime.getTime())) {
              throw new Error(`无效的时间值: ${timeValue}`)
            }

            return {
              time: parsedTime,
              pressure: parseFloat(row[pressureColumn]) / 1000, // 除以1000
              originalRow: row
            }
          })

        // 按时间排序
        validData.sort((a, b) => a.time.getTime() - b.time.getTime())

        if (validData.length === 0) {
          throw new Error('没有有效的数据可处理')
        }

        // 2. 确定时间范围
        const startTime = new Date(validData[0].time)
        const endTime = new Date(validData[validData.length - 1].time)

        // 将开始时间调整到当天的00:00:00
        const dayStart = new Date(startTime)
        dayStart.setHours(0, 0, 0, 0)

        // 将结束时间调整到当天的23:59:00
        const dayEnd = new Date(dayStart)
        dayEnd.setHours(23, 59, 0, 0)

        // 3. 生成每分钟的时间点 (1440个点)
        const result = []
        let lastValidPressure = validData[0].pressure // 用于填充缺失数据

        for (let minute = 0; minute < 1440; minute++) {
          const currentMinute = new Date(dayStart)
          currentMinute.setMinutes(minute)

          // 查找这一分钟内的第一条数据
          const minuteData = validData.find((item) => {
            const itemMinute = new Date(item.time)
            itemMinute.setSeconds(0, 0) // 忽略秒和毫秒
            return itemMinute.getTime() === currentMinute.getTime()
          })

          let pressureValue
          if (minuteData) {
            pressureValue = minuteData.pressure
            lastValidPressure = pressureValue // 更新最后有效值
          } else {
            // 使用上一次的有效值填充
            pressureValue = lastValidPressure
          }

          result.push({
            采集时间: formatDateTime(currentMinute),
            末端压力: pressureValue.toFixed(3)
          })
        }

        return result
      }

      // 格式化日期时间
      function formatDateTime(date) {
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        const seconds = String(date.getSeconds()).padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      }

      // 显示处理后的数据
      function displayProcessedData() {
        if (processedData.length === 0) return

        const headers = Object.keys(processedData[0])
        displayTable(headers, processedData)

        // 更新统计信息
        const stats = document.getElementById('stats')
        stats.innerHTML = `
          <strong>处理后数据统计：</strong>
          共 ${headers.length} 列，${processedData.length} 行数据 (每分钟一条，覆盖24小时)
        `
      }

      // 导出处理后的数据
      function exportProcessedData() {
        if (processedData.length === 0) {
          showError('没有处理后的数据可导出')
          return
        }

        const csv = convertToCSV(processedData)
        const now = new Date()
        const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_')
        const filename = `processed_data_${timestamp}.csv`

        downloadCSV(csv, filename)

        // 显示成功信息
        const processInfo = document.getElementById('processInfo')
        processInfo.innerHTML += `<br><strong>✅ 数据已导出为: ${filename}</strong>`
      }
    </script>
  </body>
</html>
